#!/usr/bin/env python3
"""
测试语法修复是否有效
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

load_dotenv(override=True)

def test_import():
    """测试是否能正常导入模块"""
    try:
        from agent_review.enhanced_main_pipe import EnhancedAgenticRAG
        print("✅ 模块导入成功")
        return True
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_initialization():
    """测试是否能正常初始化"""
    try:
        from agent_review.enhanced_main_pipe import EnhancedAgenticRAG
        
        # 创建实例
        rag = EnhancedAgenticRAG(
            min_content_length=50,
            max_content_length=1000,
            merge_window=3,
            overlap_ratio=0.1,
        )
        print("✅ 实例创建成功")
        print(f"📋 配置信息: {rag.get_insertion_stats()}")
        return True
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False

def main():
    print("🧪 开始测试语法修复...")
    
    # 测试导入
    if not test_import():
        return False
    
    # 测试初始化
    if not test_initialization():
        return False
    
    print("🎉 所有测试通过！语法修复成功。")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
