#!/usr/bin/env python3
"""
测试JSON解析优化
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

load_dotenv(override=True)

def test_normalize_response():
    """测试响应标准化功能"""
    from agent_review.enhanced_main_pipe import EnhancedAgenticRAG
    
    rag = EnhancedAgenticRAG()
    
    # 测试用例1: 字符串格式的空数组
    test_case_1 = {
        "consulting_company_info": "[]",
        "selling_user_info": "[]",
        "negative_news": "[]",
        "major_complaints": "[]",
        "request_contact_information": "[]",
        "spam_messages": "[]"
    }
    
    result_1 = rag._normalize_response(test_case_1)
    print("测试用例1 - 字符串格式的空数组:")
    print(f"输入: {test_case_1}")
    print(f"输出: {result_1}")
    
    # 验证所有值都是空列表
    for key, value in result_1.items():
        assert isinstance(value, list), f"{key} should be a list"
        assert len(value) == 0, f"{key} should be empty"
    print("✅ 测试用例1通过\n")
    
    # 测试用例2: 混合格式
    test_case_2 = {
        "consulting_company_info": ["What is your company address?"],
        "selling_user_info": "[]",
        "negative_news": '["Some negative news"]',
        "major_complaints": "",
        "request_contact_information": "Can you provide your phone number?",
        "spam_messages": []
    }
    
    result_2 = rag._normalize_response(test_case_2)
    print("测试用例2 - 混合格式:")
    print(f"输入: {test_case_2}")
    print(f"输出: {result_2}")
    
    # 验证结果
    assert result_2["consulting_company_info"] == ["What is your company address?"]
    assert result_2["selling_user_info"] == []
    assert result_2["negative_news"] == ["Some negative news"]
    assert result_2["major_complaints"] == []
    assert result_2["request_contact_information"] == ["Can you provide your phone number?"]
    assert result_2["spam_messages"] == []
    print("✅ 测试用例2通过\n")
    
    return True

def test_extract_json_from_string():
    """测试从字符串中提取JSON"""
    from agent_review.enhanced_main_pipe import EnhancedAgenticRAG
    
    rag = EnhancedAgenticRAG()
    
    # 测试用例1: 标准JSON对象
    json_string_1 = '{"consulting_company_info": [], "selling_user_info": []}'
    result_1 = rag._extract_json_from_string(json_string_1)
    print("测试用例1 - 标准JSON对象:")
    print(f"输入: {json_string_1}")
    print(f"输出: {result_1}")
    assert result_1 is not None
    assert isinstance(result_1, dict)
    print("✅ 测试用例1通过\n")
    
    # 测试用例2: 包含额外文本的JSON
    json_string_2 = 'Here is the result: {"consulting_company_info": ["test"], "selling_user_info": []} End of result.'
    result_2 = rag._extract_json_from_string(json_string_2)
    print("测试用例2 - 包含额外文本的JSON:")
    print(f"输入: {json_string_2}")
    print(f"输出: {result_2}")
    assert result_2 is not None
    assert isinstance(result_2, dict)
    print("✅ 测试用例2通过\n")
    
    # 测试用例3: 数组格式
    json_string_3 = '[{"consulting_company_info": [], "selling_user_info": []}]'
    result_3 = rag._extract_json_from_string(json_string_3)
    print("测试用例3 - 数组格式:")
    print(f"输入: {json_string_3}")
    print(f"输出: {result_3}")
    assert result_3 is not None
    assert isinstance(result_3, dict)
    print("✅ 测试用例3通过\n")
    
    return True

def main():
    print("🧪 开始测试JSON解析优化...")
    
    try:
        # 测试响应标准化
        if not test_normalize_response():
            return False
        
        # 测试JSON提取
        if not test_extract_json_from_string():
            return False
        
        print("🎉 所有JSON解析优化测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
