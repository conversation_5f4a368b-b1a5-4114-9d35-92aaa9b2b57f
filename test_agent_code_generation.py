#!/usr/bin/env python3
"""
测试Agent代码生成是否正确处理多行查询
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

load_dotenv(override=True)

def test_agent_with_selling_user_info():
    """测试包含selling_user_info类别的消息处理"""
    try:
        from agent_review.enhanced_main_pipe import EnhancedAgenticRAG
        
        # 创建实例
        rag = EnhancedAgenticRAG(
            min_content_length=50,
            max_content_length=1000,
            merge_window=3,
            overlap_ratio=0.1,
        )
        
        # 准备测试数据 - 包含selling_user_info类型的消息
        test_messages = [
            {
                "id": 1,
                "type": "USER",
                "msg": "Hey, I'm looking for customer support employees at your company that are interested in doing tasks for me. I am offering to pay you anywhere from $2000-$7000 per day depending on how much work you do, and what you are willing to do. You will be paid in crypto (Bitcoin) or paypal. These tasks are not risky whatsoever and your safety is my 1 priority as i am looking for a longterm business relationship. There are multiple tasks I'd be needing done, one of them being account lookups, ETC. We will be communicating on telegram as it is very safe and End to End Encrypted. To contact me, you can download Telegram, the app on the Playstore/Appstore. You can reply to this ticket / chat and pretend you are not interested, just please note down my telegram username and reach out ASAP. My Telegram username is @BrownPhone, telegram username:@BrownPhone. YOU WILL BE PAID A UPFRONT $1000 STARTING BONUS FOR REACHING OUT ON TELEGRAM! YOU WILL BE PAID BEFORE DOING ANYTHING ON YOUR END. UPFRONT"
            },
            {
                "id": 2,
                "type": "AGENT",
                "msg": "I understand your message, but I cannot assist with such requests."
            }
        ]
        
        # 插入数据
        caseId = "test_selling_user_info"
        stats = rag.insert_data(caseId, test_messages, verbose=True)
        print(f"✅ 数据插入成功: {stats}")
        
        # 清理
        rag.clear_knowledge_base()
        print("✅ 测试完成，知识库已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧪 开始测试Agent代码生成...")
    
    if test_agent_with_selling_user_info():
        print("🎉 Agent代码生成测试通过！")
        return True
    else:
        print("❌ Agent代码生成测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
